import React from 'react';
import { User } from '@/contexts/AuthContext';
import { TemplateType } from '@/pages/DigitalPortfolio';
import ModernBlueTemplate from './templates/ModernBlueTemplate';
import ElegantRoseTemplate from './templates/ElegantRoseTemplate';
import ProfessionalDarkTemplate from './templates/ProfessionalDarkTemplate';

interface PortfolioRendererProps {
  user: User;
  template: TemplateType;
  customizations?: {
    primaryColor?: string;
    secondaryColor?: string;
    fontFamily?: string;
    fontSize?: 'small' | 'medium' | 'large';
  };
  className?: string;
}

const PortfolioRenderer: React.FC<PortfolioRendererProps> = ({
  user,
  template,
  customizations,
  className
}) => {
  // Apply customizations via CSS variables
  const customStyles = customizations ? {
    '--primary-color': customizations.primaryColor,
    '--secondary-color': customizations.secondaryColor,
    '--font-family': customizations.fontFamily,
    '--font-size': customizations.fontSize === 'small' ? '0.875rem' : 
                   customizations.fontSize === 'large' ? '1.125rem' : '1rem'
  } as React.CSSProperties : {};

  const renderTemplate = () => {
    switch (template) {
      case 'modern-blue':
        return <ModernBlueTemplate user={user} className={className} />;
      case 'elegant-rose':
        return <ElegantRoseTemplate user={user} className={className} />;
      case 'professional-dark':
        return <ProfessionalDarkTemplate user={user} className={className} />;
      case 'creative-gradient':
        // TODO: Implement creative gradient template
        return (
          <div className="bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 min-h-screen flex items-center justify-center text-white">
            <div className="text-center">
              <h1 className="text-4xl font-bold mb-4">Creative Gradient Template</h1>
              <p className="text-xl">Coming Soon...</p>
            </div>
          </div>
        );
      default:
        return <ModernBlueTemplate user={user} className={className} />;
    }
  };

  return (
    <div style={customStyles} className="portfolio-renderer">
      {renderTemplate()}
    </div>
  );
};

export default PortfolioRenderer;
